# 扑克牌显示器 Web 服务

这是一个基于Go和WebSocket的扑克牌显示器Web应用，支持实时接收和显示扑克牌数据。

## 功能特点

- 🃏 **实时扑克牌显示**：通过WebSocket实时接收扑克牌数据
- 🔐 **灵活的登录方式**：支持登录页面和直接URL访问
- 🎨 **精美的UI设计**：真实的扑克牌渲染效果
- 📱 **响应式设计**：支持桌面和移动设备
- ⚡ **实时更新**：WebSocket确保数据实时同步

## 快速开始

### 1. 启动服务器

```bash
go run main.go
```

服务器将在 `http://localhost:8080` 启动

### 2. 访问方式

#### 方式一：登录页面
访问 `http://localhost:8080/login`
- 输入您的账号（例如：C250727503）
- 点击"连接并进入扑克牌显示器"按钮

#### 方式二：直接访问
直接访问 `http://localhost:8080/home?Account=您的账号`
- 例如：`http://localhost:8080/home?Account=C250727503`
- 系统会自动使用URL中的账号连接WebSocket

## 项目结构

```
├── main.go              # Go后端服务器
├── go.mod              # Go模块文件
├── poker_cards.html    # 扑克牌显示页面
├── poker_cards.css     # 样式文件
├── poker_cards.js      # 前端JavaScript逻辑
└── README.md           # 项目说明
```

## 技术栈

### 后端
- **Go**: HTTP服务器和路由处理
- **html/template**: 模板引擎
- **net/http**: HTTP服务

### 前端
- **HTML5**: 页面结构
- **CSS3**: 样式和动画
- **JavaScript**: WebSocket通信和交互逻辑

## API 路由

| 路由 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 重定向到登录页面 |
| `/login` | GET | 显示登录页面 |
| `/home` | GET | 扑克牌显示页面（需要Account参数） |
| `/static/*` | GET | 静态文件服务 |

## WebSocket 连接

- **服务器地址**: `ws://localhost:5311`
- **连接协议**: JSON消息格式
- **账号验证**: 通过type="web"消息注册

## 使用说明

1. **启动WebSocket服务器**（端口5311）
2. **启动Web服务器**：`go run main.go`
3. **访问登录页面**或**直接访问扑克牌页面**
4. **输入有效账号**进行连接
5. **查看实时扑克牌数据**

## 功能特性

### 扑克牌显示
- 支持标准52张牌 + 大小王
- 真实扑克牌布局和样式
- 点击选择/取消选择功能
- 轮次分组显示

### 交互功能
- 键盘快捷键支持
- 右键菜单（可扩展）
- 数据导出功能
- 实时状态显示

### 连接管理
- 自动重连机制
- 连接状态显示
- 错误处理和提示
- 账号信息显示

## 开发说明

### 修改WebSocket地址
在 `poker_cards.js` 中修改：
```javascript
pokerWsClient = new PokerWebSocketClient('ws://localhost:5311', userAccount);
```

### 修改默认账号
在 `getUserAccount()` 函数中修改默认账号：
```javascript
// 默认账号（兜底）
return 'C250727503';
```

### 自定义样式
修改 `poker_cards.css` 文件来自定义扑克牌和页面样式。

## 故障排除

1. **WebSocket连接失败**
   - 确保WebSocket服务器在端口5311运行
   - 检查防火墙设置

2. **页面无法访问**
   - 确保Go服务器正常启动
   - 检查端口8080是否被占用

3. **账号无效**
   - 确保输入的账号格式正确
   - 检查WebSocket服务器是否接受该账号

## 许可证

MIT License
