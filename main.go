package main

import (
	"fmt"
	"html/template"
	"log"
	"net/http"
	"path/filepath"
)

// 登录页面模板
const loginTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扑克牌显示器 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
            min-width: 400px;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
            font-size: 16px;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="text"]:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .description {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .direct-link {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }
        
        .direct-link p {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .direct-link code {
            background: #ecf0f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🃏 扑克牌显示器</h1>
        <div class="description">
            请输入您的账号以连接WebSocket服务器
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="account">账号:</label>
                <input type="text" id="account" name="account" placeholder="请输入账号，例如：C250727503" required>
            </div>
            
            <button type="submit" class="btn">连接并进入扑克牌显示器</button>
        </form>
        
        <div class="direct-link">
            <p>您也可以直接通过URL访问：</p>
            <code>/home?Account=您的账号</code>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const account = document.getElementById('account').value.trim();
            
            if (!account) {
                alert('请输入账号');
                return;
            }
            
            // 跳转到扑克牌显示页面
            window.location.href = '/home?Account=' + encodeURIComponent(account);
        });
        
        // 自动聚焦到输入框
        document.getElementById('account').focus();
        
        // 回车键提交
        document.getElementById('account').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
`

func main() {
	// 设置静态文件服务
	http.Handle("/static/", http.StripPrefix("/static/", http.FileServer(http.Dir("."))))
	
	// 登录页面路由
	http.HandleFunc("/login", loginHandler)
	
	// 扑克牌显示页面路由
	http.HandleFunc("/home", homeHandler)
	
	// 根路径重定向到登录页面
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			http.Redirect(w, r, "/login", http.StatusFound)
		} else {
			http.NotFound(w, r)
		}
	})
	
	fmt.Println("服务器启动在 http://localhost:8080")
	fmt.Println("访问 http://localhost:8080/login 进行登录")
	fmt.Println("或直接访问 http://localhost:8080/home?Account=您的账号")
	
	log.Fatal(http.ListenAndServe(":8080", nil))
}

// 登录页面处理器
func loginHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	fmt.Fprint(w, loginTemplate)
}

// 扑克牌显示页面处理器
func homeHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	// 获取账号参数
	account := r.URL.Query().Get("Account")
	if account == "" {
		// 如果没有账号参数，重定向到登录页面
		http.Redirect(w, r, "/login", http.StatusFound)
		return
	}
	
	// 读取并修改HTML文件
	tmpl, err := template.ParseFiles("poker_cards.html")
	if err != nil {
		// 如果模板文件不存在，返回错误
		http.Error(w, "Template file not found: "+err.Error(), http.StatusInternalServerError)
		return
	}
	
	// 传递账号参数到模板
	data := struct {
		Account string
	}{
		Account: account,
	}
	
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	err = tmpl.Execute(w, data)
	if err != nil {
		http.Error(w, "Template execution error: "+err.Error(), http.StatusInternalServerError)
		return
	}
}
